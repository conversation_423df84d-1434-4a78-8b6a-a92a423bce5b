# Possible changes for next major version

- Allow plural resource names generation based on global/per-area config
- When generating documentation, take care of same-named classes in different namespaces. If we use the ->shortName() from reflection we would have conflicts.
    - This would happen if the classes are in different namespaces but have the same name and area.
- Allow list of directories for resource classes for area
- Support serializer v2
