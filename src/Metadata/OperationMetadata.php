<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle\Metadata;

use <PERSON><PERSON><PERSON>\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Enum\Pagination;

class OperationMetadata
{
    /**
     * @param class-string|array{class-string, string} $controller
     * @param array<string, string>                    $urlRequirements
     * @param array<PathParameterMetadata>             $pathParameters
     * @param array<QueryParameterMetadata>            $queryParameters
     * @param array<ResponseMetadata>                  $responses
     * @param array<string, scalar|string[]>           $normalizationContext
     * @param array<string, scalar|string[]>           $denormalizationContext
     * @param array<FilterMetadata>                    $filters
     * @param ?class-string                            $input
     * @param ?class-string                            $output
     * @param array<HeaderMetadata>                    $headers
     * @param array<int, string>                       $responseCodesLogLevel
     */
    public function __construct(
        public string $name,
        public string|array $controller,
        public ?string $security,
        public string $summary,
        public string $description,

        // Request
        public string $type,
        public string $method,
        public string $uriTemplate,
        public string $fullUrlTemplate,
        public array $urlRequirements,
        public int $routePriority,
        public array $pathParameters,
        public array $queryParameters,
        public array $denormalizationContext,
        public array $filters,
        public ?string $input,
        public ContentType $requestType,
        public string $requestDescription,
        public array $headers,
        public ?string $requestOpenApiSchemaName,

        // Response
        public Pagination $pagination,
        public int $maxPerPage,
        public int $defaultPerPage,
        public array $responses,
        public string $responseDescription,
        public array $normalizationContext,
        public int $successHttpCode,
        public ?string $output,
        public ContentType $responseType,
        public ?string $responseOpenApiSchemaName,
        public array $responseCodesLogLevel,
    ) {
    }
}
