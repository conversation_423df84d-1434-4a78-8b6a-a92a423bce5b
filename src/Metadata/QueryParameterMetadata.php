<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Metadata;

readonly class QueryParameterMetadata
{
    /**
     * @param array<scalar>|null $enumValues
     */
    public function __construct(
        public string $name,
        public string $type,
        public bool $required,
        public string $description,
        public ?string $format = null,
        public ?array $enumValues = null,
        public bool $multipleValues = false,
        public ?int $minimum = null,
        public ?int $maximum = null,
    ) {
    }
}
