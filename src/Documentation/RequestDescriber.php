<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Documentation;

use <PERSON>k<PERSON>\OpenApi\In;
use <PERSON><PERSON>ky\OpenApi\MediaType;
use Do<PERSON>ky\OpenApi\Operation;
use <PERSON>k<PERSON>\OpenApi\Parameter;
use <PERSON><PERSON><PERSON>\OpenApi\RequestBody;
use <PERSON><PERSON><PERSON>\OpenApi\Schema;
use Do<PERSON><PERSON>\Undefined;
use PreZero\ApiBundle\Attribute\Value\Header;
use PreZero\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Metadata\OperationMetadata;
use PreZero\ApiBundle\Metadata\ResourceMetadata;

class RequestDescriber
{
    /**
     * @var array<Header>
     */
    private array $globalRequestHeaders = [];

    public function __construct(
        private readonly Dokky $dokky,
    ) {
    }

    /**
     * @param array<Header> $globalRequestHeaders
     */
    public function setGlobalRequestHeaders(array $globalRequestHeaders): self
    {
        $this->globalRequestHeaders = $globalRequestHeaders;

        return $this;
    }

    public function describeRequest(
        ResourceMetadata $resourceMetadata,
        OperationMetadata $operationMetadata,
        Operation $openApiOperation,
    ): void {
        $this->describeRequestBody($operationMetadata, $resourceMetadata, $openApiOperation);
        $this->describeRequestHeaders($operationMetadata, $openApiOperation);
        $this->describePathParameters($operationMetadata, $openApiOperation);
        $this->describeQueryParameters($operationMetadata, $openApiOperation);
    }

    private function describeRequestBody(
        OperationMetadata $operationMetadata,
        ResourceMetadata $resourceMetadata,
        Operation $openApiOperation,
    ): void {
        /** @var string[]|null $groups */
        $groups = $operationMetadata->denormalizationContext['groups'] ?? null;

        match ($operationMetadata->requestType) {
            ContentType::EMPTY => null,
            ContentType::BINARY => $openApiOperation->requestBody = new RequestBody(
                content: [
                    'application/octet-stream' => new MediaType(
                        schema: new Schema(
                            type: Schema\Type::STRING,
                            format: 'binary',
                        ),
                    ),
                ],
                description: $operationMetadata->requestDescription,
                required: true,
            ),
            ContentType::DTO => $openApiOperation->requestBody = new RequestBody(
                content: [
                    'application/json' => new MediaType(
                        schema: new Schema(ref: $this->getSchemaRef($operationMetadata, $resourceMetadata, $groups)),
                    ),
                ],
                description: $operationMetadata->requestDescription,
                required: true,
            ),
        };
    }

    private function describeRequestHeaders(OperationMetadata $operationMetadata, Operation $openApiOperation): void
    {
        if ([] === $this->globalRequestHeaders && [] === $operationMetadata->headers) {
            return;
        }

        if ($openApiOperation->parameters instanceof Undefined) {
            $openApiOperation->parameters = [];
        }

        foreach ($this->globalRequestHeaders as $header) {
            /** @phpstan-ignore-next-line https://github.com/phpstan/phpstan/issues/7880 */
            $openApiOperation->parameters[] = new Parameter(
                name: $header->name,
                in: In::HEADER,
                description: $header->description,
                required: $header->required,
                allowEmptyValue: false,
                schema: new Schema(
                    type: Schema\Type::from($header->type),
                    enum: $header->enumValues ?: Undefined::VALUE,
                    format: $header->format ?: Undefined::VALUE,
                ),
                example: $header->example,
            );
        }

        foreach ($operationMetadata->headers as $header) {
            /** @phpstan-ignore-next-line https://github.com/phpstan/phpstan/issues/7880 */
            $openApiOperation->parameters[] = new Parameter(
                name: $header->name,
                in: In::HEADER,
                description: $header->description,
                required: $header->required,
                allowEmptyValue: false,
                schema: new Schema(
                    type: Schema\Type::from($header->type),
                    enum: $header->enumValues ?: Undefined::VALUE,
                    format: $header->format ?: Undefined::VALUE,
                ),
                example: $header->example,
            );
        }
    }

    public function describePathParameters(
        OperationMetadata $operationMetadata,
        Operation $openApiOperation,
    ): void {
        if ([] === $operationMetadata->pathParameters) {
            return;
        }

        if ($openApiOperation->parameters instanceof Undefined) {
            $openApiOperation->parameters = [];
        }

        foreach ($operationMetadata->pathParameters as $pathParameter) {
            /** @phpstan-ignore-next-line https://github.com/phpstan/phpstan/issues/7880 */
            $openApiOperation->parameters[] = new Parameter(
                name: $pathParameter->name,
                in: In::PATH,
                description: $pathParameter->description,
                required: true,
                allowEmptyValue: false,
                schema: new Schema(
                    type: Schema\Type::from($pathParameter->type),
                    pattern: $pathParameter->constraint ?: Undefined::VALUE,
                ),
            );
        }
    }

    public function describeQueryParameters(
        OperationMetadata $operationMetadata,
        Operation $openApiOperation,
    ): void {
        if ([] === $operationMetadata->queryParameters && [] === $operationMetadata->filters) {
            return;
        }

        if ($openApiOperation->parameters instanceof Undefined) {
            $openApiOperation->parameters = [];
        }

        foreach ($operationMetadata->queryParameters as $queryParameter) {
            if ($queryParameter->multipleValues) {
                $name = $queryParameter->name.'[]';
                $schema = new Schema(
                    type: Schema\Type::ARRAY,
                    items: new Schema(
                        type: Schema\Type::from($queryParameter->type),
                        enum: $queryParameter->enumValues ?: Undefined::VALUE,
                        format: $queryParameter->format ?: Undefined::VALUE,
                        minimum: $queryParameter->minimum ?: Undefined::VALUE,
                        maximum: $queryParameter->maximum ?: Undefined::VALUE,
                    ),
                );
            } else {
                $name = $queryParameter->name;
                $schema = new Schema(
                    type: Schema\Type::from($queryParameter->type),
                    enum: $queryParameter->enumValues ?: Undefined::VALUE,
                    format: $queryParameter->format ?: Undefined::VALUE,
                    minimum: $queryParameter->minimum ?: Undefined::VALUE,
                    maximum: $queryParameter->maximum ?: Undefined::VALUE,
                );
            }

            /** @phpstan-ignore-next-line https://github.com/phpstan/phpstan/issues/7880 */
            $openApiOperation->parameters[] = new Parameter(
                name: $name,
                in: In::QUERY,
                description: $queryParameter->description,
                required: $queryParameter->required,
                allowEmptyValue: false,
                schema: $schema,
            );
        }

        foreach ($operationMetadata->filters as $filterMetadata) {
            if ($filterMetadata->multiple) {
                $name = $filterMetadata->parameterName.'[]';
                $schema = new Schema(
                    type: Schema\Type::ARRAY,
                    items: new Schema(
                        type: Schema\Type::from($filterMetadata->parameterType),
                        enum: $filterMetadata->parameterEnumValues ?: Undefined::VALUE,
                        format: $filterMetadata->parameterFormat ?: Undefined::VALUE,
                    ),
                );
            } else {
                $name = $filterMetadata->parameterName;
                $schema = new Schema(
                    type: Schema\Type::from($filterMetadata->parameterType),
                    enum: $filterMetadata->parameterEnumValues ?: Undefined::VALUE,
                    format: $filterMetadata->parameterFormat ?: Undefined::VALUE,
                );
            }

            /** @phpstan-ignore-next-line https://github.com/phpstan/phpstan/issues/7880 */
            $openApiOperation->parameters[] = new Parameter(
                name: $name,
                in: In::QUERY,
                description: $filterMetadata->parameterDescription ?? Undefined::VALUE,
                required: $filterMetadata->required,
                allowEmptyValue: false,
                schema: $schema,
            );
        }
    }

    /**
     * @param array<string>|null $groups
     */
    private function getSchemaRef(
        OperationMetadata $operationMetadata,
        ResourceMetadata $resourceMetadata,
        ?array $groups,
    ): string {
        if (null === $operationMetadata->requestOpenApiSchemaName) {
            return $this->dokky->componentsRegistry()->getSchemaReference(
                className: $operationMetadata->input ?? $resourceMetadata->resourceClass,
                groups: $groups,
            );
        }

        return $this->dokky->componentsRegistry()->getNamedSchemaReference(
            className: $operationMetadata->input ?? $resourceMetadata->resourceClass,
            schemaName: $operationMetadata->requestOpenApiSchemaName,
            groups: $groups,
        );
    }
}
