<?php

declare(strict_types=1);

namespace PreZero\ApiBundle;

use PreZero\ApiBundle\Exception\ApiBundleException;

/**
 * @phpstan-type Header array{
 *     format: ?string,
 *     description?: string,
 *     required?: bool,
 *     enum_values?: array<string>,
 *     example?: string,
 *     type?: string,
 * }
 * @phpstan-type AreaConfiguration array{
 *     resource_path: string,
 *     url_prefix?: string,
 *     global_request_headers?: array<string, Header>,
 *     open_api?: array<mixed>,
 *     stateless?: bool,
 *     max_per_page?: int,
 *     default_per_page?: int,
 * }
 * @phpstan-type BundleConfiguration array{
 *     consider_nullable_properties_as_optional?: bool,
 *     areas: array<string, AreaConfiguration>,
 *     max_per_page: int,
 *     default_per_page: int,
 * }
 *
 * @internal
 */
readonly class Configuration
{
    public const int DEFAULT_MAX_PER_PAGE = 100;
    public const int DEFAULT_PER_PAGE = 25;

    /**
     * @param BundleConfiguration $bundleConfiguration
     */
    public function __construct(
        private array $bundleConfiguration,
    ) {
    }

    /**
     * @return BundleConfiguration
     */
    public function getBundleConfiguration(): array
    {
        return $this->bundleConfiguration;
    }

    /**
     * @return AreaConfiguration
     *
     * @throws ApiBundleException
     */
    public function getAreaConfiguration(string $area): array
    {
        if (!isset($this->bundleConfiguration['areas'][$area])) {
            throw new ApiBundleException(
                sprintf(
                    'Area "%s" not found in the bundle configuration. Available areas: %s',
                    $area,
                    implode(', ', array_keys($this->bundleConfiguration['areas'])),
                )
            );
        }

        return $this->bundleConfiguration['areas'][$area];
    }
}
