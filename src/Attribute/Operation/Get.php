<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle\Attribute\Operation;

use Pre<PERSON><PERSON>\ApiBundle\Attribute\Value\Filter;
use Pre<PERSON>ero\ApiBundle\Attribute\Value\Header;
use Pre<PERSON>ero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Attribute\Value\QueryParameter;
use PreZero\ApiBundle\Attribute\Value\Response;
use PreZero\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Enum\Pagination;

class Get extends Operation
{
    public const string HTTP_METHOD = 'GET';

    /**
     * @param class-string|array{class-string, string} $controller               Controller reference - either [ClassName::class, 'methodName'] for regular controllers or ClassName::class for invokable controllers
     * @param array<PathParameter>|null                $pathParameters
     * @param array<QueryParameter>                    $queryParameters
     * @param array<Response>                          $responses
     * @param array<string, mixed>                     $normalizationContext
     * @param array<string, mixed>                     $denormalizationContext
     * @param array<Filter>                            $filters
     * @param ?class-string                            $input
     * @param ?class-string                            $output
     * @param array<Header>                            $additionalRequestHeaders
     */
    public function __construct(
        string|array $controller,
        ?string $name = null,
        string $summary = '',
        string $description = '',
        ?string $security = null,

        // Request
        ?string $uriTemplate = null,
        int $routePriority = 0,
        ?array $pathParameters = null,
        array $queryParameters = [],
        bool $omitDefaultQueryParameters = false,
        ContentType $requestType = ContentType::EMPTY,
        string $requestDescription = '',
        array $denormalizationContext = [],
        array $filters = [],
        ?string $input = null,
        array $additionalRequestHeaders = [],

        // Response
        string $responseDescription = '',
        array $responses = [],
        ContentType $responseType = ContentType::DTO,
        array $normalizationContext = [],
        ?string $output = null,
        int $successHttpCode = 200,
        ?string $responseOpenApiSchemaName = null,
        public array $responseCodesLogLevel = [],
    ) {
        parent::__construct(
            controller: $controller,
            name: $name,
            summary: $summary,
            description: $description,
            security: $security,
            uriTemplate: $uriTemplate,
            routePriority: $routePriority,
            pathParameters: $pathParameters,
            queryParameters: $queryParameters,
            omitDefaultQueryParameters: $omitDefaultQueryParameters,
            requestType: $requestType,
            requestDescription: $requestDescription,
            denormalizationContext: $denormalizationContext,
            filters: $filters,
            input: $input,
            additionalRequestHeaders: $additionalRequestHeaders,
            requestOpenApiSchemaName: null,
            pagination: Pagination::NONE,
            maxPerPage: null,
            defaultPerPage: null,
            responseDescription: $responseDescription,
            responses: $responses,
            responseType: $responseType,
            normalizationContext: $normalizationContext,
            output: $output,
            successHttpCode: $successHttpCode,
            responseOpenApiSchemaName: $responseOpenApiSchemaName,
            responseCodesLogLevel: $responseCodesLogLevel,
        );
    }
}
