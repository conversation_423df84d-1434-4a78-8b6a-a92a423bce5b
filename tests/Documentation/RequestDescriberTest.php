<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Tests\Documentation;

use Dok<PERSON>\OpenApi\In;
use Dokky\OpenApi\Operation;
use Dokky\OpenApi\Parameter;
use Dok<PERSON>\OpenApi\Schema;
use PHPUnit\Framework\TestCase;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Documentation\Dokky;
use PreZero\ApiBundle\Documentation\RequestDescriber;
use PreZero\ApiBundle\Enum\FilterType;
use PreZero\ApiBundle\Metadata\FilterMetadata;
use PreZero\ApiBundle\Metadata\OperationMetadata;
use PreZero\ApiBundle\Metadata\PathParameterMetadata;
use PreZero\ApiBundle\Tests\Controller\DummyController;

class RequestDescriberTest extends TestCase
{
    private RequestDescriber $requestDescriber;
    private \PHPUnit\Framework\MockObject\MockObject|Dokky $dokkyMock;

    protected function setUp(): void
    {
        $this->dokkyMock = $this->createMock(Dokky::class);
        $this->requestDescriber = new RequestDescriber($this->dokkyMock);
    }

    public function testDescribeQueryParametersHandlesMultipleValueFilter(): void
    {
        // Arrange
        $filterMetadata = new FilterMetadata(
            parameterName: 'status',
            filterType: FilterType::STRING_EXACT,
            fieldName: 'statusField',
            required: false,
            parameterType: 'string',
            parameterDescription: 'Filter by status',
            parameterFormat: null,
            parameterEnumValues: ['active', 'inactive'],
            validators: [],
            multiple: true // Key part: filter allows multiple values
        );

        $operationMetadata = new OperationMetadata(
            name: 'test_dummy_get_collection_0',
            controller: [DummyController::class, 'getCollection'],
            security: null,
            summary: 'Get collection of dummies',
            description: '', // Provide an empty string instead of null
            type: GetCollection::class,
            method: 'GET',
            uriTemplate: '/dummies',
            fullUrlTemplate: '/test-api/dummies',
            urlRequirements: [],
            routePriority: 0,
            pathParameters: [],
            queryParameters: [], // No standard query params for simplicity
            denormalizationContext: [],
            filters: [$filterMetadata], // Add our multi-value filter
            input: null,
            requestType: \PreZero\ApiBundle\Enum\ContentType::EMPTY,
            requestDescription: '', // Provide an empty string instead of null
            headers: [],
            requestOpenApiSchemaName: null,
            pagination: \PreZero\ApiBundle\Enum\Pagination::NONE,
            maxPerPage: 100,
            defaultPerPage: 10,
            responses: [],
            responseDescription: '', // Provide an empty string instead of null
            normalizationContext: [],
            successHttpCode: 200,
            output: null,
            responseType: \PreZero\ApiBundle\Enum\ContentType::DTO,
            responseOpenApiSchemaName: null,
            responseCodesLogLevel: [],
        );

        $openApiOperation = new Operation();

        // Act
        $this->requestDescriber->describeQueryParameters($operationMetadata, $openApiOperation);

        // Assert
        $this->assertCount(1, $openApiOperation->parameters);
        $this->assertInstanceOf(Parameter::class, $openApiOperation->parameters[0]);

        /** @var Parameter $parameter */
        $parameter = $openApiOperation->parameters[0];

        $this->assertSame('status[]', $parameter->name); // Expect name to end with []
        $this->assertSame(In::QUERY, $parameter->in);
        $this->assertFalse($parameter->required);
        $this->assertSame('Filter by status', $parameter->description);

        $this->assertInstanceOf(Schema::class, $parameter->schema);
        $this->assertSame(Schema\Type::ARRAY, $parameter->schema->type);

        $this->assertInstanceOf(Schema::class, $parameter->schema->items);
        $this->assertSame(Schema\Type::STRING, $parameter->schema->items->type);
        $this->assertSame(['active', 'inactive'], $parameter->schema->items->enum);
    }

    public function testDescribePathParametersWithConstraints(): void
    {
        // Arrange
        $pathParameterWithConstraint = new PathParameterMetadata(
            name: 'id',
            type: 'integer',
            description: 'The item ID',
            constraint: '\d+'
        );

        $pathParameterWithoutConstraint = new PathParameterMetadata(
            name: 'slug',
            type: 'string',
            description: 'The item slug',
            constraint: null
        );

        $operationMetadata = new OperationMetadata(
            name: 'test_dummy_get_1',
            controller: [DummyController::class, 'getItem'],
            security: null,
            summary: 'Get a dummy item',
            description: '',
            type: \PreZero\ApiBundle\Attribute\Operation\Get::class,
            method: 'GET',
            uriTemplate: '/dummies/{id}/details/{slug}',
            fullUrlTemplate: '/test-api/dummies/{id}/details/{slug}',
            urlRequirements: ['id' => '\d+'],
            routePriority: 0,
            pathParameters: [$pathParameterWithConstraint, $pathParameterWithoutConstraint],
            queryParameters: [],
            denormalizationContext: [],
            filters: [],
            input: null,
            requestType: \PreZero\ApiBundle\Enum\ContentType::EMPTY,
            requestDescription: '',
            headers: [],
            requestOpenApiSchemaName: null,
            pagination: \PreZero\ApiBundle\Enum\Pagination::NONE,
            maxPerPage: 100,
            defaultPerPage: 10,
            responses: [],
            responseDescription: 'A dummy item',
            normalizationContext: [],
            successHttpCode: 200,
            output: null,
            responseType: \PreZero\ApiBundle\Enum\ContentType::EMPTY,
            responseOpenApiSchemaName: null,
            responseCodesLogLevel: [],
        );

        $openApiOperation = new Operation();

        // Act
        $this->requestDescriber->describePathParameters($operationMetadata, $openApiOperation);

        // Assert
        $this->assertCount(2, $openApiOperation->parameters);

        // Check first parameter (with constraint)
        $parameterWithConstraint = $openApiOperation->parameters[0];
        $this->assertInstanceOf(Parameter::class, $parameterWithConstraint);
        $this->assertSame('id', $parameterWithConstraint->name);
        $this->assertSame(In::PATH, $parameterWithConstraint->in);
        $this->assertTrue($parameterWithConstraint->required);
        $this->assertSame('The item ID', $parameterWithConstraint->description);
        $this->assertInstanceOf(Schema::class, $parameterWithConstraint->schema);
        $this->assertSame(Schema\Type::INTEGER, $parameterWithConstraint->schema->type);
        $this->assertSame('\d+', $parameterWithConstraint->schema->pattern);

        // Check second parameter (without constraint)
        $parameterWithoutConstraint = $openApiOperation->parameters[1];
        $this->assertInstanceOf(Parameter::class, $parameterWithoutConstraint);
        $this->assertSame('slug', $parameterWithoutConstraint->name);
        $this->assertSame(In::PATH, $parameterWithoutConstraint->in);
        $this->assertTrue($parameterWithoutConstraint->required);
        $this->assertSame('The item slug', $parameterWithoutConstraint->description);
        $this->assertInstanceOf(Schema::class, $parameterWithoutConstraint->schema);
        $this->assertSame(Schema\Type::STRING, $parameterWithoutConstraint->schema->type);
        // Pattern should be undefined when no constraint is provided
        $this->assertTrue($parameterWithoutConstraint->schema->pattern instanceof \Dokky\Undefined);
    }
}
