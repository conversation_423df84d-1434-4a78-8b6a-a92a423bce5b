<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle\Tests;

use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use PreZero\ApiBundle\ApiResources;
use PreZero\ApiBundle\Configuration;
use PreZero\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Enum\Pagination;
use PreZero\ApiBundle\Metadata\OperationMetadata;
use PreZero\ApiBundle\Metadata\ResourceMetadata;
use PreZero\ApiBundle\RouteLoader;
use PreZero\ApiBundle\Tests\Controller\TestController;
use PreZero\ApiBundle\Tests\Controller\InvokableController;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Routing\Route;
use Symfony\Component\Routing\RouteCollection;

class RouteLoaderControllerFormatTest extends TestCase
{
    private RouteLoader $routeLoader;
    private Configuration|MockObject $configuration;
    private ApiResources|MockObject $apiResources;
    private Filesystem|MockObject $filesystem;
    private string $projectDir;

    protected function setUp(): void
    {
        $this->configuration = $this->createMock(Configuration::class);
        $this->apiResources = $this->createMock(ApiResources::class);
        $this->filesystem = $this->createMock(Filesystem::class);
        $this->projectDir = __DIR__;

        $this->routeLoader = new RouteLoader(
            $this->configuration,
            $this->apiResources,
            $this->filesystem,
            $this->projectDir
        );
    }

    public function testRouteLoaderHandlesArrayControllerFormat(): void
    {
        $areaName = 'testArea';
        $pathPrefix = '/api/test';
        $areaConfig = [
            'resource_path' => '.',
            'path_prefix' => $pathPrefix,
        ];
        $bundleConfig = ['areas' => [$areaName => $areaConfig]];

        $this->configuration->method('getBundleConfiguration')->willReturn($bundleConfig);

        $expectedPath = $this->projectDir.'/'.$areaConfig['resource_path'];
        $this->filesystem->method('readlink')
            ->with($expectedPath, true)
            ->willReturn($expectedPath);

        // Create operation metadata that represents the result of parsing [TestController::class, 'getItem']
        $operationMetadata = new OperationMetadata(
            name: 'get_test_item',
            controller: [TestController::class, 'getItem'],
            security: null,
            summary: 'Get a test item',
            description: 'Retrieves a single test item by its ID.',
            type: 'PreZero\\ApiBundle\\Attribute\\Operation\\Get',
            method: 'GET',
            uriTemplate: '/items/{id}',
            fullUrlTemplate: $pathPrefix.'/items/{id}',
            urlRequirements: ['id' => '\d+'],
            routePriority: 0,
            pathParameters: [],
            queryParameters: [],
            denormalizationContext: [],
            filters: [],
            input: null,
            requestType: ContentType::EMPTY,
            requestDescription: '',
            headers: [],
            requestOpenApiSchemaName: null,
            pagination: Pagination::NONE,
            maxPerPage: 100,
            defaultPerPage: 10,
            responses: [],
            responseDescription: '',
            normalizationContext: [],
            successHttpCode: 200,
            output: null,
            responseType: ContentType::DTO,
            responseOpenApiSchemaName: null,
            responseCodesLogLevel: [],
        );

        $resourceMetadata = new ResourceMetadata(
            resourceClassNamespace: 'PreZero\\ApiBundle\\Tests',
            resourceClass: DummyResource::class,
            resourceClassShortName: 'DummyResource',
            name: 'DummyResource',
            area: $areaName,
            identifier: 'id',
            pathPrefix: $pathPrefix,
            tag: 'DummyResource',
            security: null,
            operations: [$operationMetadata],
            maxPerPage: 100,
            defaultPerPage: 10
        );

        $this->apiResources->method('getResourcesMetadataForApiArea')
            ->with($areaName)
            ->willReturn([$resourceMetadata]);

        $routeCollection = ($this->routeLoader)();

        self::assertInstanceOf(RouteCollection::class, $routeCollection);
        self::assertCount(1, $routeCollection);

        $route = $routeCollection->get('get_test_item');
        self::assertInstanceOf(Route::class, $route);

        // Verify the controller reference is correctly formatted for Symfony
        $defaults = $route->getDefaults();
        self::assertSame([TestController::class, 'getItem'], $defaults['_controller']);
    }

    public function testRouteLoaderHandlesInvokableControllerFormat(): void
    {
        $areaName = 'testArea';
        $pathPrefix = '/api/test';
        $areaConfig = [
            'resource_path' => '.',
            'path_prefix' => $pathPrefix,
        ];
        $bundleConfig = ['areas' => [$areaName => $areaConfig]];

        $this->configuration->method('getBundleConfiguration')->willReturn($bundleConfig);

        $expectedPath = $this->projectDir.'/'.$areaConfig['resource_path'];
        $this->filesystem->method('readlink')
            ->with($expectedPath, true)
            ->willReturn($expectedPath);

        // Create operation metadata that represents the result of parsing InvokableController::class
        $operationMetadata = new OperationMetadata(
            name: 'post_test_item',
            controller: InvokableController::class,
            security: null,
            summary: 'Create a test item',
            description: 'Creates a new test item.',
            type: 'PreZero\\ApiBundle\\Attribute\\Operation\\Post',
            method: 'POST',
            uriTemplate: '/items',
            fullUrlTemplate: $pathPrefix.'/items',
            urlRequirements: [],
            routePriority: 0,
            pathParameters: [],
            queryParameters: [],
            denormalizationContext: [],
            filters: [],
            input: null,
            requestType: ContentType::EMPTY,
            requestDescription: '',
            headers: [],
            requestOpenApiSchemaName: null,
            pagination: Pagination::NONE,
            maxPerPage: 100,
            defaultPerPage: 10,
            responses: [],
            responseDescription: '',
            normalizationContext: [],
            successHttpCode: 201,
            output: null,
            responseType: ContentType::DTO,
            responseOpenApiSchemaName: null,
            responseCodesLogLevel: [],
        );

        $resourceMetadata = new ResourceMetadata(
            resourceClassNamespace: 'PreZero\\ApiBundle\\Tests',
            resourceClass: DummyResource::class,
            resourceClassShortName: 'DummyResource',
            name: 'DummyResource',
            area: $areaName,
            identifier: 'id',
            pathPrefix: $pathPrefix,
            tag: 'DummyResource',
            security: null,
            operations: [$operationMetadata],
            maxPerPage: 100,
            defaultPerPage: 10
        );

        $this->apiResources->method('getResourcesMetadataForApiArea')
            ->with($areaName)
            ->willReturn([$resourceMetadata]);

        $routeCollection = ($this->routeLoader)();

        self::assertInstanceOf(RouteCollection::class, $routeCollection);
        self::assertCount(1, $routeCollection);

        $route = $routeCollection->get('post_test_item');
        self::assertInstanceOf(Route::class, $route);

        // Verify the controller reference is correctly formatted for Symfony
        $defaults = $route->getDefaults();
        self::assertSame(InvokableController::class, $defaults['_controller']);
    }
}
