<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle\Tests;

use PHPUnit\Framework\TestCase;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Configuration;
use PreZero\ApiBundle\Documentation\Dokky;
use PreZero\ApiBundle\Documentation\RequestDescriber;
use PreZero\ApiBundle\Metadata\MetadataBuilder;
use PreZero\ApiBundle\PathFormatter;
use Symfony\Component\String\Slugger\AsciiSlugger;

class OpenApiIntegrationTest extends TestCase
{
    private const array BUNDLE_CONFIGURATION = [
        'areas' => [
            'testArea' => [
                'resource_path' => 'src/Dummy',
                'url_prefix' => '/api/v1',
                'max_per_page' => 50,
                'open_api' => [
                    'info' => [
                        'title' => 'Test API',
                        'description' => 'Test API for integration testing',
                        'version' => '1.0.0',
                    ],
                ],
            ],
        ],
        'max_per_page' => 50,
        'default_per_page' => 10,
    ];

    public function testRequestDescriberIncludesPathParameterConstraints(): void
    {
        // Arrange
        $configuration = new Configuration(self::BUNDLE_CONFIGURATION);
        $metadataBuilder = new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            $configuration
        );

        $apiResource = new ApiResource(
            area: 'testArea',
            operations: [
                new Get(
                    controller: ['PreZero\\ApiBundle\\Tests\\Controller\\DummyController', 'getItem'],
                    uriTemplate: '/items/{id}/details/{slug}',
                    pathParameters: [
                        new PathParameter('id', 'integer', 'The item ID', '\d+'),
                        new PathParameter('slug', 'string', 'The item slug', '[a-z0-9-]+'),
                    ]
                ),
            ]
        );

        $resourceMetadata = $metadataBuilder->buildFromApiResource(
            TestResourceForOpenApi::class,
            $apiResource
        );

        $dokky = new Dokky($configuration);
        $requestDescriber = new RequestDescriber($dokky);

        $openApiOperation = new \Dokky\OpenApi\Operation();

        // Act
        $requestDescriber->describePathParameters($resourceMetadata->operations[0], $openApiOperation);

        // Assert
        $this->assertCount(2, $openApiOperation->parameters);

        // Check first parameter (id with constraint)
        $idParameter = $openApiOperation->parameters[0];
        $this->assertSame('id', $idParameter->name);
        $this->assertSame(\Dokky\OpenApi\In::PATH, $idParameter->in);
        $this->assertTrue($idParameter->required);
        $this->assertSame('The item ID', $idParameter->description);
        $this->assertSame(\Dokky\OpenApi\Schema\Type::INTEGER, $idParameter->schema->type);
        $this->assertSame('\d+', $idParameter->schema->pattern);

        // Check second parameter (slug with constraint)
        $slugParameter = $openApiOperation->parameters[1];
        $this->assertSame('slug', $slugParameter->name);
        $this->assertSame(\Dokky\OpenApi\In::PATH, $slugParameter->in);
        $this->assertTrue($slugParameter->required);
        $this->assertSame('The item slug', $slugParameter->description);
        $this->assertSame(\Dokky\OpenApi\Schema\Type::STRING, $slugParameter->schema->type);
        $this->assertSame('[a-z0-9-]+', $slugParameter->schema->pattern);
    }

    public function testRequestDescriberWithoutConstraintsDoesNotIncludePattern(): void
    {
        // Arrange
        $configuration = new Configuration(self::BUNDLE_CONFIGURATION);
        $metadataBuilder = new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            $configuration
        );

        $apiResource = new ApiResource(
            area: 'testArea',
            operations: [
                new Get(
                    controller: ['PreZero\\ApiBundle\\Tests\\Controller\\DummyController', 'getItem'],
                    uriTemplate: '/items/{id}',
                    pathParameters: [
                        new PathParameter('id', 'string', 'The item ID'), // No constraint
                    ]
                ),
            ]
        );

        $resourceMetadata = $metadataBuilder->buildFromApiResource(
            TestResourceForOpenApi::class,
            $apiResource
        );

        $dokky = new Dokky($configuration);
        $requestDescriber = new RequestDescriber($dokky);

        $openApiOperation = new \Dokky\OpenApi\Operation();

        // Act
        $requestDescriber->describePathParameters($resourceMetadata->operations[0], $openApiOperation);

        // Assert
        $this->assertCount(1, $openApiOperation->parameters);

        // Check parameter (no constraint)
        $idParameter = $openApiOperation->parameters[0];
        $this->assertSame('id', $idParameter->name);
        $this->assertSame(\Dokky\OpenApi\In::PATH, $idParameter->in);
        $this->assertTrue($idParameter->required);
        $this->assertSame('The item ID', $idParameter->description);
        $this->assertSame(\Dokky\OpenApi\Schema\Type::STRING, $idParameter->schema->type);
        // Pattern should be undefined when no constraint is provided
        $this->assertTrue($idParameter->schema->pattern instanceof \Dokky\Undefined);
    }
}

class TestResourceForOpenApi
{
    public string $id;
}
