<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Tests\EventListener;

use PHPUnit\Framework\TestCase;
use PreZero\ApiBundle\ApiRequestMetadata;
use PreZero\ApiBundle\ApiRequestMetadataResolver;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\ErrorResponse\ValidationError;
use PreZero\ApiBundle\EventListener\ControllerValueResolver;
use PreZero\ApiBundle\Exception\ValidationException;
use PreZero\ApiBundle\Metadata\OperationMetadata;
use PreZero\ApiBundle\Metadata\ResourceMetadata;
use PreZero\ApiBundle\Serializer\SerializerInterface;
use PreZero\ApiBundle\Tests\Controller\TestController;
use PreZero\ApiBundle\Tests\Fixtures\Dto\ValidatedInputDto;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadata;
use Symfony\Component\Validator\Validation;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ControllerValueResolverTest extends TestCase
{
    private \PHPUnit\Framework\MockObject\MockObject|SerializerInterface $serializer;
    private ValidatorInterface $validator;
    private \PHPUnit\Framework\MockObject\MockObject|ApiRequestMetadataResolver $apiRequestMetadataResolver;
    private \PHPUnit\Framework\MockObject\MockObject|LoggerInterface $logger;
    private ControllerValueResolver $controllerValueResolver;

    protected function setUp(): void
    {
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->validator = Validation::createValidatorBuilder()->enableAttributeMapping()->getValidator();
        $this->apiRequestMetadataResolver = $this->createMock(ApiRequestMetadataResolver::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->controllerValueResolver = new ControllerValueResolver(
            $this->serializer,
            $this->validator,
            $this->apiRequestMetadataResolver,
            $this->logger,
        );
    }

    public function testResolveThrowsValidationExceptionOnValidationErrors(): void
    {
        // Arrange
        $request = new Request([], [], [], [], [], ['REQUEST_METHOD' => 'POST'], '{"name": "a"}');
        $argument = new ArgumentMetadata('dto', ValidatedInputDto::class, false, false, null);
        $dto = new ValidatedInputDto('a'); // Invalid DTO

        $apiResource = new ApiResource('test-area', []);
        $resourceMetadata = new ResourceMetadata(
            resourceClassNamespace: 'PreZero\\ApiBundle\\Tests\\Fixtures\\Dto',
            resourceClass: ValidatedInputDto::class,
            resourceClassShortName: 'ValidatedInputDto',
            name: 'ValidatedInputDto',
            area: $apiResource->area,
            identifier: $apiResource->identifier,
            pathPrefix: '/test-area-api',
            tag: $apiResource->tag,
            security: $apiResource->security,
            operations: [],
            maxPerPage: 100,
            defaultPerPage: 10,
        );
        $operationMetadata = new OperationMetadata(
            name: 'test_post',
            controller: [TestController::class, 'postAction'],
            security: null,
            summary: '',
            description: '',
            type: Post::class,
            method: 'POST',
            uriTemplate: '/test',
            fullUrlTemplate: '/test',
            urlRequirements: [],
            routePriority: 0,
            pathParameters: [],
            queryParameters: [],
            denormalizationContext: [],
            filters: [],
            input: ValidatedInputDto::class,
            requestType: \PreZero\ApiBundle\Enum\ContentType::DTO,
            requestDescription: '',
            headers: [],
            requestOpenApiSchemaName: null,
            pagination: \PreZero\ApiBundle\Enum\Pagination::NONE,
            maxPerPage: 100,
            defaultPerPage: 10,
            responses: [],
            responseDescription: '',
            normalizationContext: [],
            successHttpCode: 201,
            output: null,
            responseType: \PreZero\ApiBundle\Enum\ContentType::EMPTY,
            responseOpenApiSchemaName: null,
            responseCodesLogLevel: [],
        );
        $apiRequestMetadata = new ApiRequestMetadata($resourceMetadata, $operationMetadata);

        $this->apiRequestMetadataResolver->expects($this->any())->method('resolveCurrentOperation')->willReturn($apiRequestMetadata);
        $this->serializer->expects($this->any())->method('deserialize')->willReturn($dto);

        // Act & Assert
        try {
            // Need to iterate to trigger the resolver logic
            iterator_to_array($this->controllerValueResolver->resolve($request, $argument));
            $this->fail('Expected ValidationException was not thrown.');
        } catch (ValidationException $e) {
            $errors = $e->getValidationErrors();
            $this->assertCount(1, $errors);
            $this->assertInstanceOf(ValidationError::class, $errors[0]);
            $this->assertSame('name', $errors[0]->field);
            // Exact message depends on the Symfony Validator version, but it should mention the length constraint.
            $this->assertStringContainsString('This value is too short. It should have 3 characters or more.', $errors[0]->message);
        }
    }

    public function testResolveReturnsDtoWhenValidationPasses(): void
    {
        // Arrange
        $request = new Request([], [], [], [], [], ['REQUEST_METHOD' => 'POST'], '{"name": "valid name"}');
        $argument = new ArgumentMetadata('dto', ValidatedInputDto::class, false, false, null);
        $dto = new ValidatedInputDto('valid name'); // Valid DTO

        $apiResource = new ApiResource('test-area', []);
        $resourceMetadata = new ResourceMetadata(
            resourceClassNamespace: 'PreZero\\ApiBundle\\Tests\\Fixtures\\Dto',
            resourceClass: ValidatedInputDto::class,
            resourceClassShortName: 'ValidatedInputDto',
            name: 'ValidatedInputDto',
            area: $apiResource->area,
            identifier: $apiResource->identifier,
            pathPrefix: '/test-area-api',
            tag: $apiResource->tag,
            security: $apiResource->security,
            operations: [],
            maxPerPage: 100,
            defaultPerPage: 10,
        );
        $operationMetadata = new OperationMetadata(
            name: 'test_post',
            controller: [TestController::class, 'postAction'],
            security: null,
            summary: '',
            description: '',
            type: Post::class,
            method: 'POST',
            uriTemplate: '/test',
            fullUrlTemplate: '/test',
            urlRequirements: [],
            routePriority: 0,
            pathParameters: [],
            queryParameters: [],
            denormalizationContext: [],
            filters: [],
            input: ValidatedInputDto::class,
            requestType: \PreZero\ApiBundle\Enum\ContentType::DTO,
            requestDescription: '',
            headers: [],
            requestOpenApiSchemaName: null,
            pagination: \PreZero\ApiBundle\Enum\Pagination::NONE,
            maxPerPage: 100,
            defaultPerPage: 10,
            responses: [],
            responseDescription: '',
            normalizationContext: [],
            successHttpCode: 201,
            output: null,
            responseType: \PreZero\ApiBundle\Enum\ContentType::EMPTY,
            responseOpenApiSchemaName: null,
            responseCodesLogLevel: [],
        );
        $apiRequestMetadata = new ApiRequestMetadata($resourceMetadata, $operationMetadata);

        $this->apiRequestMetadataResolver->expects($this->any())->method('resolveCurrentOperation')->willReturn($apiRequestMetadata);
        $this->serializer->expects($this->any())->method('deserialize')->willReturn($dto);

        // Act
        $result = iterator_to_array($this->controllerValueResolver->resolve($request, $argument));

        // Assert
        $this->assertCount(1, $result);
        $this->assertSame($dto, $result[0]);
    }
}
