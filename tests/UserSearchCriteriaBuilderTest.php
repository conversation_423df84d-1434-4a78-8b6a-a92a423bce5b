<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace PreZero\ApiBundle\Tests;

use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use PreZero\ApiBundle\ApiRequestMetadata;
use PreZero\ApiBundle\ApiRequestMetadataResolver;
use PreZero\ApiBundle\Enum\FilterType;
use PreZero\ApiBundle\Metadata\FilterMetadata;
use PreZero\ApiBundle\Metadata\OperationMetadata;
use PreZero\ApiBundle\Tests\Controller\TestController;
use PreZero\ApiBundle\UserCriteria\MatchStrategy;
use PreZero\ApiBundle\UserCriteria\UserFilter;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Component\HttpFoundation\Request;
use PreZero\ApiBundle\Exception\ValidationException;
use Symfony\Component\Validator\Constraints\Choice;
use Symfony\Component\Validator\Validation;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class UserSearchCriteriaBuilderTest extends TestCase
{
    private MockObject&ApiRequestMetadataResolver $metadataResolverMock;
    private ValidatorInterface $validator;
    private UserSearchCriteriaBuilder $builder;

    protected function setUp(): void
    {
        $this->metadataResolverMock = $this->createMock(ApiRequestMetadataResolver::class);
        $this->validator = Validation::createValidatorBuilder()
            ->enableAttributeMapping()
            ->getValidator();

        $this->builder = new UserSearchCriteriaBuilder(
            $this->metadataResolverMock,
            $this->validator
        );
    }

    /**
     * @param array<string, FilterMetadata> $filters
     */
    private function mockOperationMetadata(array $filters): void
    {
        $operationMetadata = new OperationMetadata(
            name: 'test_operation',
            controller: [TestController::class, 'testAction'],
            security: null,
            summary: 'Test Summary',
            description: 'Test Description',
            type: 'test_type',
            method: 'GET',
            uriTemplate: '/test',
            fullUrlTemplate: '/api/test',
            urlRequirements: [],
            routePriority: 0,
            pathParameters: [],
            queryParameters: [],
            denormalizationContext: [],
            filters: $filters,
            input: null,
            requestType: \PreZero\ApiBundle\Enum\ContentType::EMPTY,
            requestDescription: '',
            headers: [],
            requestOpenApiSchemaName: null,
            pagination: \PreZero\ApiBundle\Enum\Pagination::NONE,
            maxPerPage: 50,
            defaultPerPage: 10,
            responses: [],
            responseDescription: '',
            normalizationContext: [],
            successHttpCode: 200,
            output: null,
            responseType: \PreZero\ApiBundle\Enum\ContentType::EMPTY,
            responseOpenApiSchemaName: null,
            responseCodesLogLevel: []
        );

        $apiRequestMetadataMock = $this->createMock(ApiRequestMetadata::class);

        $reflectionApiMeta = new \ReflectionClass($apiRequestMetadataMock);
        $opMetaProperty = $reflectionApiMeta->getProperty('operationMetadata');
        $opMetaProperty->setValue($apiRequestMetadataMock, $operationMetadata);

        $this->metadataResolverMock
            ->method('resolveCurrentOperation')
            ->willReturn($apiRequestMetadataMock);
    }
    // =================== Single Value Tests ===================

    public function testBuildsCriteriaWithSingleValueFilter(): void
    {
        // Arrange
        $filtersMetadata = [
            'status' => new FilterMetadata(
                parameterName: 'status',
                filterType: FilterType::STRING_EXACT,
                fieldName: 'statusField',
                multiple: false // Explicitly false
            ),
        ];
        $this->mockOperationMetadata($filtersMetadata);
        $request = new Request(query: ['status' => 'active']);

        // Act
        $criteria = $this->builder->fromSymfonyRequest($request);

        // Assert
        self::assertArrayHasKey('status', $criteria->filters);
        $filter = $criteria->filters['status'];
        self::assertInstanceOf(UserFilter::class, $filter);
        self::assertSame('statusField', $filter->field);
        self::assertSame('active', $filter->value);
        self::assertSame(MatchStrategy::EXACT, $filter->matchStrategy);
    }

    public function testBuildsCriteriaWithSingleValueFilterValidationSuccess(): void
    {
        // Arrange
        $validator = new Choice(['active', 'inactive']);
        $filtersMetadata = [
            'status' => new FilterMetadata(
                parameterName: 'status',
                filterType: FilterType::STRING_EXACT,
                fieldName: 'statusField',
                validators: [$validator],
                multiple: false
            ),
        ];
        $this->mockOperationMetadata($filtersMetadata);
        $request = new Request(query: ['status' => 'active']);

        // Act
        $criteria = $this->builder->fromSymfonyRequest($request);

        // Assert
        self::assertArrayHasKey('status', $criteria->filters);
        self::assertSame('active', $criteria->filters['status']->value);
    }

    public function testThrowsExceptionOnSingleValueFilterValidationFailure(): void
    {
        // Arrange
        $validator = new Choice(['active', 'inactive']);
        $filtersMetadata = [
            'status' => new FilterMetadata(
                parameterName: 'status',
                filterType: FilterType::STRING_EXACT,
                fieldName: 'statusField',
                validators: [$validator],
                multiple: false
            ),
        ];
        $this->mockOperationMetadata($filtersMetadata);
        $request = new Request(query: ['status' => 'pending']); // Invalid value

        // Assert
        try {
            // Act
            $this->builder->fromSymfonyRequest($request);
            self::fail('Expected ValidationException was not thrown.');
        } catch (ValidationException $e) {
            self::assertSame('Invalid value(s) for query parameter', $e->getMessage());
            self::assertCount(1, $e->getValidationErrors());
            $error = $e->getValidationErrors()[0];
            self::assertSame('status', $error->field);
            self::assertStringContainsString('The value you selected is not a valid choice.', $error->message);
        }
    }

    public function testBuildsCriteriaWithMultiValueFilter(): void
    {
        // Arrange
        $filtersMetadata = [
            'status' => new FilterMetadata(
                parameterName: 'status',
                filterType: FilterType::STRING_EXACT,
                fieldName: 'statusField',
                multiple: true,
            ),
        ];
        $this->mockOperationMetadata($filtersMetadata);
        $request = new Request(query: ['status' => ['active', 'pending']]);

        // Act
        $criteria = $this->builder->fromSymfonyRequest($request);

        // Assert
        self::assertArrayHasKey('status', $criteria->filters);
        $filter = $criteria->filters['status'];
        self::assertInstanceOf(UserFilter::class, $filter);
        self::assertSame('statusField', $filter->field);
        self::assertSame(['active', 'pending'], $filter->value);
        self::assertSame(MatchStrategy::EXACT, $filter->matchStrategy);
    }

    public function testBuildsCriteriaWithMultiValueFilterSingleValueProvided(): void
    {
        // Arrange
        $filtersMetadata = [
            'status' => new FilterMetadata(
                parameterName: 'status',
                filterType: FilterType::STRING_EXACT,
                fieldName: 'statusField',
                multiple: true
            ),
        ];
        $this->mockOperationMetadata($filtersMetadata);
        $request = new Request(query: ['status' => ['active']]);

        // Act
        $criteria = $this->builder->fromSymfonyRequest($request);

        // Assert
        self::assertArrayHasKey('status', $criteria->filters);
        $filter = $criteria->filters['status'];
        self::assertSame(['active'], $filter->value); // Expect array with single element
    }

    public function testBuildsCriteriaWithMultiValueFilterValidationSuccess(): void
    {
        // Arrange
        $validator = new Choice(['active', 'pending', 'archived']);
        $filtersMetadata = [
            'status' => new FilterMetadata(
                parameterName: 'status',
                filterType: FilterType::STRING_EXACT,
                fieldName: 'statusField',
                validators: [$validator],
                multiple: true
            ),
        ];
        $this->mockOperationMetadata($filtersMetadata);
        $request = new Request(query: ['status' => ['active', 'pending']]);

        // Act
        $criteria = $this->builder->fromSymfonyRequest($request);

        // Assert
        self::assertArrayHasKey('status', $criteria->filters);
        self::assertSame(['active', 'pending'], $criteria->filters['status']->value);
    }

    public function testThrowsExceptionOnMultiValueFilterValidationFailureForOneValue(): void
    {
        // Arrange
        $validator = new Choice(['active', 'pending', 'archived']);
        $filtersMetadata = [
            'status' => new FilterMetadata(
                parameterName: 'status',
                filterType: FilterType::STRING_EXACT,
                fieldName: 'statusField',
                validators: [$validator],
                multiple: true
            ),
        ];
        $this->mockOperationMetadata($filtersMetadata);
        $request = new Request(query: ['status' => ['active', 'invalid']]);

        // Assert
        try {
            // Act
            $this->builder->fromSymfonyRequest($request);
            self::fail('Expected ValidationException was not thrown.');
        } catch (ValidationException $e) {
            self::assertSame('Invalid value(s) for query parameter', $e->getMessage());
            self::assertCount(1, $e->getValidationErrors());
            $error = $e->getValidationErrors()[0];
            self::assertSame('status', $error->field);
            self::assertStringContainsString('The value you selected is not a valid choice.', $error->message);
        }
    }

    public function testBuildsCriteriaWithMultiValueFilterIgnoresEmptyStringsInArray(): void
    {
        // Arrange
        $filtersMetadata = [
            'tags' => new FilterMetadata(parameterName: 'tags', multiple: true),
        ];
        $this->mockOperationMetadata($filtersMetadata);
        $request = new Request(query: ['tags' => ['tag1', '', 'tag2', '']]);

        // Act
        $criteria = $this->builder->fromSymfonyRequest($request);

        // Assert
        self::assertArrayHasKey('tags', $criteria->filters);
        self::assertSame(['tag1', 'tag2'], $criteria->filters['tags']->value);
    }

    public function testIgnoresFilterIfNotInRequest(): void
    {
        // Arrange
        $filtersMetadata = [
            'status' => new FilterMetadata(parameterName: 'status', multiple: false),
            'category' => new FilterMetadata(parameterName: 'category', multiple: true),
        ];
        $this->mockOperationMetadata($filtersMetadata);
        $request = new Request(query: ['otherParam' => 'value']); // Filters not present

        // Act
        $criteria = $this->builder->fromSymfonyRequest($request);

        // Assert
        self::assertEmpty($criteria->filters);
    }

    public function testIgnoresFilterIfValueIsNull(): void
    {
        // Arrange
        $filtersMetadata = [
            'status' => new FilterMetadata(parameterName: 'status', multiple: false),
        ];
        $this->mockOperationMetadata($filtersMetadata);
        // Simulate how Request might handle a param without a value (?status) - often becomes null
        $request = new Request(query: ['status' => null]);

        // Act
        $criteria = $this->builder->fromSymfonyRequest($request);

        // Assert
        self::assertEmpty($criteria->filters);
    }

    public function testIgnoresFilterIfValueIsEmptyStringAndNotRequired(): void
    {
        // Arrange
        $filtersMetadata = [
            'status' => new FilterMetadata(parameterName: 'status', required: false, multiple: false), // Not required
        ];
        $this->mockOperationMetadata($filtersMetadata);
        $request = new Request(query: ['status' => '']); // Empty string

        // Act
        $criteria = $this->builder->fromSymfonyRequest($request);

        // Assert
        self::assertArrayNotHasKey('status', $criteria->filters); // Should not be added as a filter
    }

    public function testIgnoresMultiValueFilterIfValueIsEmptyArray(): void
    {
        // Arrange
        $filtersMetadata = [
            'tags' => new FilterMetadata(parameterName: 'tags', multiple: true),
        ];
        $this->mockOperationMetadata($filtersMetadata);
        // Simulate key[] without value, results in empty array
        $request = new Request(query: ['tags' => []]);

        // Act
        $criteria = $this->builder->fromSymfonyRequest($request);

        // Assert
        self::assertEmpty($criteria->filters);
    }

    public function testIgnoresMultiValueFilterIfValueIsArrayOfEmptyStrings(): void
    {
        // Arrange
        $filtersMetadata = [
            'tags' => new FilterMetadata(parameterName: 'tags', multiple: true),
        ];
        $this->mockOperationMetadata($filtersMetadata);
        // Simulate key[]=&key[]=&key[]= which results in an array of empty strings
        $request = new Request(query: ['tags' => ['', '', '']]);

        // Act
        $criteria = $this->builder->fromSymfonyRequest($request);

        // Assert
        self::assertEmpty($criteria->filters);
    }
}
