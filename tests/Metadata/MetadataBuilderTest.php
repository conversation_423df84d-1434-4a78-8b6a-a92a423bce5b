<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace PreZero\ApiBundle\Tests\Metadata;

use PHPUnit\Framework\TestCase;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Delete;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Operation\Put;
use PreZero\ApiBundle\Configuration;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Metadata\FilterMetadata;
use PreZero\ApiBundle\Metadata\MetadataBuilder;
use PreZero\ApiBundle\Metadata\OperationMetadata;
use PreZero\ApiBundle\Metadata\ResourceMetadata;
use PreZero\ApiBundle\PathFormatter;
use Symfony\Component\String\Slugger\AsciiSlugger;

#[ApiResource(
    area: 'testArea',
    operations: [
        new Get(
            controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'getItem'],
            name: 'get_dummy',
            uriTemplate: '/dummies/{id}'
        ),
        new Post(
            controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'createItem'],
            name: 'create_dummy'
        ),
        new Put(
            controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'updateItem'],
            name: 'put_dummy',
            uriTemplate: '/dummies/{id}'
        ),
        new Patch(
            controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'patchItem'],
            name: 'patch_dummy',
            uriTemplate: '/dummies/{id}'
        ),
        new Delete(
            controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'deleteItem'],
            name: 'delete_dummy',
            uriTemplate: '/dummies/{id}'
        ),
    ],
)]
class DummyResourceForMetadata
{
    public string $id;
    public string $customId;
}

class MetadataBuilderTest extends TestCase
{
    private const array BUNDLE_CONFIGURATION = [
        'areas' => [
            'testArea' => [
                'resource_path' => 'src/Dummy',
                'url_prefix' => '/api/v1',
                'max_per_page' => Configuration::DEFAULT_MAX_PER_PAGE,
            ],
        ],
        'max_per_page' => Configuration::DEFAULT_MAX_PER_PAGE,
        'default_per_page' => Configuration::DEFAULT_PER_PAGE,
    ];

    public function testBuildFromApiResource(): void
    {
        $bundleConfiguration = self::BUNDLE_CONFIGURATION;

        $metadataBuilder = new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            new Configuration($bundleConfiguration)
        );

        // Arrange
        $resourceClass = DummyResourceForMetadata::class;
        $reflectionClass = new \ReflectionClass($resourceClass);
        /** @var ApiResource $apiResourceAttribute */
        $apiResourceAttribute = $reflectionClass->getAttributes(ApiResource::class)[0]->newInstance();

        // Act
        $resourceMetadata = $metadataBuilder->buildFromApiResource($resourceClass, $apiResourceAttribute);

        // Assert Resource Metadata
        self::assertInstanceOf(ResourceMetadata::class, $resourceMetadata);
        self::assertSame($resourceClass, $resourceMetadata->resourceClass);
        self::assertSame('DummyResourceForMetadata', $resourceMetadata->resourceClassShortName);
        self::assertSame('testArea', $resourceMetadata->area);
        self::assertSame('id', $resourceMetadata->identifier);

        // Assert Operations Metadata
        $operations = $resourceMetadata->operations;
        self::assertCount(5, $operations);
        self::assertArrayHasKey(0, $operations);
        self::assertArrayHasKey(1, $operations);
        self::assertArrayHasKey(2, $operations);
        self::assertArrayHasKey(3, $operations);
        self::assertArrayHasKey(4, $operations);

        // Assert GET operation details
        $getOperation = $operations[0];
        self::assertInstanceOf(OperationMetadata::class, $getOperation);
        self::assertSame('get_dummy', $getOperation->name);
        self::assertSame(Get::class, $getOperation->type);
        self::assertSame('GET', $getOperation->method);
        // PathFormatter mock returns prefix + operationPath
        self::assertSame('/api/v1/dummies/{id}', $getOperation->fullUrlTemplate);

        // Assert POST operation details
        $postOperation = $operations[1];
        self::assertInstanceOf(OperationMetadata::class, $postOperation);
        self::assertSame('create_dummy', $postOperation->name);
        self::assertSame(Post::class, $postOperation->type);
        self::assertSame('POST', $postOperation->method);
        // PathFormatter mock returns prefix + resourcePath (since Post has no path)
        self::assertSame('/api/v1/dummy-resource-for-metadata', $postOperation->fullUrlTemplate);

        // Assert PUT operation details
        $putOperation = $operations[2];
        self::assertInstanceOf(OperationMetadata::class, $putOperation);
        self::assertSame('put_dummy', $putOperation->name);
        self::assertSame(Put::class, $putOperation->type);
        self::assertSame('PUT', $putOperation->method);
        // PathFormatter mock returns prefix + operationPath
        self::assertSame('/api/v1/dummies/{id}', $putOperation->fullUrlTemplate);

        // Assert PATCH operation details
        $patchOperation = $operations[3];
        self::assertInstanceOf(OperationMetadata::class, $patchOperation);
        self::assertSame('patch_dummy', $patchOperation->name);
        self::assertSame(Patch::class, $patchOperation->type);
        self::assertSame('PATCH', $patchOperation->method);
        // PathFormatter mock returns prefix + operationPath
        self::assertSame('/api/v1/dummies/{id}', $patchOperation->fullUrlTemplate);

        // Assert DELETE operation details
        $deleteOperation = $operations[4];
        self::assertInstanceOf(OperationMetadata::class, $deleteOperation);
        self::assertSame('delete_dummy', $deleteOperation->name);
        self::assertSame(Delete::class, $deleteOperation->type);
        self::assertSame('DELETE', $deleteOperation->method);
        // PathFormatter mock returns prefix + operationPath
        self::assertSame('/api/v1/dummies/{id}', $deleteOperation->fullUrlTemplate);
    }

    public function testBuildFromApiResourceThrowsExceptionWhenResourceClassDoesNotExist(): void
    {
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Could not reflect class NonExistentClass');

        $bundleConfiguration = self::BUNDLE_CONFIGURATION;

        $metadataBuilder = new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            new Configuration($bundleConfiguration)
        );

        $metadataBuilder->buildFromApiResource(
            // @phpstan-ignore-next-line
            'NonExistentClass',
            new ApiResource(
                area: 'testArea',
                operations: []
            )
        );
    }

    public function testBuildIdentifierThrowsExceptionWhenIdentifierPropertyDoesNotExist(): void
    {
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage(
            'Could not find identifier property "nonExistentId" in class "PreZero\ApiBundle\Tests\Metadata\DummyResourceForMetadata". '.
            'If you want to use a different identifier, please specify it in the ApiResource attribute.'
        );

        $bundleConfiguration = self::BUNDLE_CONFIGURATION;

        $metadataBuilder = new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            new Configuration($bundleConfiguration)
        );

        $resourceClass = DummyResourceForMetadata::class;
        $metadataBuilder->buildFromApiResource(
            $resourceClass,
            new ApiResource(
                area: 'testArea',
                operations: [],
                identifier: 'nonExistentId'
            )
        );
    }

    public function testBuildOperationMetadataThrowsExceptionWhenControllerClassDoesNotExist(): void
    {
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage(
            'Could not find controller action "nonExistentAction" in controller "PreZero\ApiBundle\Tests\Controller\DummyController".'
        );

        $bundleConfiguration = self::BUNDLE_CONFIGURATION;

        $metadataBuilder = new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            new Configuration($bundleConfiguration)
        );

        $resourceClass = DummyResourceForMetadata::class;
        $metadataBuilder->buildFromApiResource(
            $resourceClass,
            new ApiResource(
                area: 'testArea',
                operations: [
                    new Get(
                        controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'nonExistentAction']
                    ),
                ]
            )
        );
    }

    public function testBuildOperationMetadataThrowsExceptionWhenControllerActionDoesNotExist(): void
    {
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Could not find controller class "NonExistentController".');

        $bundleConfiguration = self::BUNDLE_CONFIGURATION;

        $metadataBuilder = new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            new Configuration($bundleConfiguration)
        );

        $resourceClass = DummyResourceForMetadata::class;
        $metadataBuilder->buildFromApiResource(
            $resourceClass,
            new ApiResource(
                area: 'testArea',
                operations: [
                    new Get(
                        controller: ['NonExistentController', 'getItem']
                    ),
                ]
            )
        );
    }

    public function testBuildUriTemplateThrowsExceptionWhenOperationTypeIsUnknown(): void
    {
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Unknown operation type "PreZero\ApiBundle\Tests\Metadata\DummyOperation"');

        $bundleConfiguration = self::BUNDLE_CONFIGURATION;

        $metadataBuilder = new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            new Configuration($bundleConfiguration)
        );

        $resourceClass = DummyResourceForMetadata::class;
        $metadataBuilder->buildFromApiResource(
            $resourceClass,
            new ApiResource(
                area: 'testArea',
                operations: [
                    new DummyOperation(
                        controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'getItem'],
                        name: 'dummy',
                        summary: 'dummy',
                        description: 'dummy'
                    ),
                ]
            )
        );
    }

    public function testBuildFromApiResourceWithDifferentIdentifier(): void
    {
        $bundleConfiguration = self::BUNDLE_CONFIGURATION;

        $metadataBuilder = new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            new Configuration($bundleConfiguration)
        );

        // Arrange
        $resourceClass = DummyResourceForMetadata::class;
        $apiResourceAttribute = new ApiResource(
            area: 'testArea',
            operations: [],
            identifier: 'customId'
        );

        // Act
        $resourceMetadata = $metadataBuilder->buildFromApiResource($resourceClass, $apiResourceAttribute);

        // Assert Resource Metadata
        self::assertInstanceOf(ResourceMetadata::class, $resourceMetadata);
        self::assertSame($resourceClass, $resourceMetadata->resourceClass);
        self::assertSame('DummyResourceForMetadata', $resourceMetadata->resourceClassShortName);
        self::assertSame('testArea', $resourceMetadata->area);
        self::assertSame('customId', $resourceMetadata->identifier);
    }

    public function testBuildFromApiResourceWithEmptyOperations(): void
    {
        $bundleConfiguration = self::BUNDLE_CONFIGURATION;

        $metadataBuilder = new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            new Configuration($bundleConfiguration)
        );

        // Arrange
        $resourceClass = DummyResourceForMetadata::class;
        $apiResourceAttribute = new ApiResource(
            area: 'testArea',
            operations: []
        );

        // Act
        $resourceMetadata = $metadataBuilder->buildFromApiResource($resourceClass, $apiResourceAttribute);

        // Assert Resource Metadata
        self::assertInstanceOf(ResourceMetadata::class, $resourceMetadata);
        self::assertSame($resourceClass, $resourceMetadata->resourceClass);
        self::assertSame('DummyResourceForMetadata', $resourceMetadata->resourceClassShortName);
        self::assertSame('testArea', $resourceMetadata->area);
        self::assertSame('id', $resourceMetadata->identifier);
        self::assertCount(0, $resourceMetadata->operations);
    }

    public function testGetOperationThrowsExceptionWhenOperationNotFound(): void
    {
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage(
            'Operation "non_existent_operation" not found in resource "PreZero\ApiBundle\Tests\Metadata\DummyResourceForMetadata".'
        );

        // Arrange: Build metadata using the builder
        $bundleConfiguration = self::BUNDLE_CONFIGURATION;
        $metadataBuilder = new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            new Configuration($bundleConfiguration)
        );
        $resourceClass = DummyResourceForMetadata::class;
        $reflectionClass = new \ReflectionClass($resourceClass);
        /** @var ApiResource $apiResourceAttribute */
        $apiResourceAttribute = $reflectionClass->getAttributes(ApiResource::class)[0]->newInstance();
        $resourceMetadata = $metadataBuilder->buildFromApiResource($resourceClass, $apiResourceAttribute);

        // Act & Assert Exception
        $resourceMetadata->getOperation('non_existent_operation');
    }

    public function testBuildNormalizationContext(): void
    {
        $bundleConfiguration = self::BUNDLE_CONFIGURATION;

        $metadataBuilder = new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            new Configuration($bundleConfiguration)
        );

        $operation = new Get(
            controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'getItem'],
            normalizationContext: ['groups' => ['test']]
        );

        $resourceClass = DummyResourceForMetadata::class;
        $apiResourceAttribute = new ApiResource(
            area: 'testArea',
            operations: [$operation]
        );
        $resourceMetadata = $metadataBuilder->buildFromApiResource($resourceClass, $apiResourceAttribute);
        $operationMetadata = $resourceMetadata->operations[0];

        self::assertSame(['groups' => ['test', 'Default', 'default']], $operationMetadata->normalizationContext);
    }

    public function testBuildDenormalizationContext(): void
    {
        $bundleConfiguration = self::BUNDLE_CONFIGURATION;

        $metadataBuilder = new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            new Configuration($bundleConfiguration),
        );

        $operation = new Get(
            controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'getItem'],
            denormalizationContext: ['groups' => ['test']]
        );

        $resourceClass = DummyResourceForMetadata::class;
        $apiResourceAttribute = new ApiResource(
            area: 'testArea',
            operations: [$operation]
        );
        $resourceMetadata = $metadataBuilder->buildFromApiResource($resourceClass, $apiResourceAttribute);
        $operationMetadata = $resourceMetadata->operations[0];

        self::assertSame(['groups' => ['test', 'Default', 'default']], $operationMetadata->denormalizationContext);
        self::assertSame(['groups' => ['test', 'Default', 'default']], $operationMetadata->denormalizationContext);
    }

    public function testBuildFiltersSetsMultipleFlag(): void
    {
        $bundleConfiguration = self::BUNDLE_CONFIGURATION;

        $metadataBuilder = new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            new Configuration($bundleConfiguration)
        );

        $operation = new GetCollection(
            controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'getItems'],
            filters: [
                new Filter(parameterName: 'status', multiple: true),
                new Filter(parameterName: 'category'), // Default multiple: false
                new Filter(parameterName: 'tag', multiple: false),
            ]
        );

        $resourceClass = DummyResourceForMetadata::class;
        $apiResourceAttribute = new ApiResource(
            area: 'testArea',
            operations: [$operation]
        );
        $resourceMetadata = $metadataBuilder->buildFromApiResource($resourceClass, $apiResourceAttribute);
        $operationMetadata = $resourceMetadata->operations[0];

        self::assertArrayHasKey('status', $operationMetadata->filters);
        self::assertInstanceOf(FilterMetadata::class, $operationMetadata->filters['status']);
        self::assertTrue($operationMetadata->filters['status']->multiple);

        self::assertArrayHasKey('category', $operationMetadata->filters);
        self::assertInstanceOf(FilterMetadata::class, $operationMetadata->filters['category']);
        self::assertFalse($operationMetadata->filters['category']->multiple);

        self::assertArrayHasKey('tag', $operationMetadata->filters);
        self::assertInstanceOf(FilterMetadata::class, $operationMetadata->filters['tag']);
        self::assertFalse($operationMetadata->filters['tag']->multiple);
    }

    public function testBuildFromApiResourceWithPerPageConfiguration(): void
    {
        $bundleConfiguration = [
            'max_per_page' => 30,
            'default_per_page' => 10,
            'areas' => [
                'testArea' => [
                    'resource_path' => 'src/Dummy',
                    'url_prefix' => '/api/v1',
                    'max_per_page' => 75,
                ],
            ],
        ];

        $metadataBuilder = new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            new Configuration($bundleConfiguration)
        );

        // Arrange
        $resourceClass = DummyResourceForMetadata::class;
        $apiResourceAttribute = new ApiResource(
            area: 'testArea',
            operations: [
                new GetCollection(
                    controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'getItems'],
                    name: 'get_dummies',
                    maxPerPage: 15,
                    defaultPerPage: 5
                ),
            ],
        );

        // Act
        $resourceMetadata = $metadataBuilder->buildFromApiResource($resourceClass, $apiResourceAttribute);
        $operationMetadata = $resourceMetadata->operations[0];

        // Assert Resource Metadata
        self::assertSame(75, $resourceMetadata->maxPerPage);
        self::assertSame(10, $resourceMetadata->defaultPerPage);

        // Assert Operation Metadata
        self::assertSame(15, $operationMetadata->maxPerPage);
        self::assertSame(5, $operationMetadata->defaultPerPage);
    }

    public function testMetadataBuilderPassesPriorityFromOperationToMetadata(): void
    {
        // Arrange
        $bundleConfiguration = [
            'areas' => [
                'testArea' => [
                    'url_prefix' => '/api/test',
                    'max_per_page' => 75,
                    'default_per_page' => 10,
                ],
            ],
            'max_per_page' => 30,
            'default_per_page' => 10,
        ];

        $metadataBuilder = new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            new Configuration($bundleConfiguration)
        );

        $resourceClass = DummyResourceForMetadata::class;
        $apiResourceAttribute = new ApiResource(
            area: 'testArea',
            operations: [
                new Get(
                    controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'getItem'],
                    name: 'get_dummy_with_priority',
                    routePriority: 42
                ),
                new Post(
                    controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'createItem'],
                    name: 'create_dummy_default_priority'
                    // No priority specified, should default to 0
                ),
            ],
        );

        // Act
        $resourceMetadata = $metadataBuilder->buildFromApiResource($resourceClass, $apiResourceAttribute);
        $getOperationMetadata = $resourceMetadata->operations[0];
        $postOperationMetadata = $resourceMetadata->operations[1];

        // Assert
        self::assertSame(42, $getOperationMetadata->routePriority);
        self::assertSame(0, $postOperationMetadata->routePriority);
    }
}
