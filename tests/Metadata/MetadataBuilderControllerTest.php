<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle\Tests\Metadata;

use PHPUnit\Framework\TestCase;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Configuration;
use PreZero\ApiBundle\Metadata\MetadataBuilder;
use PreZero\ApiBundle\PathFormatter;
use PreZero\ApiBundle\Tests\Controller\TestController;
use PreZero\ApiBundle\Tests\Controller\InvokableController;
use Symfony\Component\String\Slugger\AsciiSlugger;

class MetadataBuilderControllerTest extends TestCase
{
    private MetadataBuilder $metadataBuilder;

    protected function setUp(): void
    {
        $configuration = new Configuration([
            'areas' => [
                'test' => [
                    'resource_path' => 'tests',
                    'url_prefix' => '/api/test',
                    'max_per_page' => 50,
                    'default_per_page' => 10,
                ],
            ],
            'max_per_page' => 50,
            'default_per_page' => 10,
        ]);

        $this->metadataBuilder = new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            $configuration
        );
    }

    public function testArrayControllerFormatConversion(): void
    {
        $apiResource = new ApiResource(
            area: 'test',
            operations: [
                new Get(controller: [TestController::class, 'getItem']),
            ]
        );

        $resourceMetadata = $this->metadataBuilder->buildFromApiResource(
            TestResource::class,
            $apiResource
        );

        $operation = $resourceMetadata->operations[0];
        $this->assertSame([TestController::class, 'getItem'], $operation->controller);
    }

    public function testStringControllerFormatConversion(): void
    {
        $apiResource = new ApiResource(
            area: 'test',
            operations: [
                new Get(controller: InvokableController::class),
            ]
        );

        $resourceMetadata = $this->metadataBuilder->buildFromApiResource(
            TestResource::class,
            $apiResource
        );

        $operation = $resourceMetadata->operations[0];
        $this->assertSame(InvokableController::class, $operation->controller);
    }

    public function testMultipleOperationsWithDifferentControllerFormats(): void
    {
        $apiResource = new ApiResource(
            area: 'test',
            operations: [
                new Get(controller: [TestController::class, 'getItem']),
                new Post(controller: InvokableController::class),
            ]
        );

        $resourceMetadata = $this->metadataBuilder->buildFromApiResource(
            TestResource::class,
            $apiResource
        );

        $getOperation = $resourceMetadata->operations[0];
        $this->assertSame([TestController::class, 'getItem'], $getOperation->controller);

        $postOperation = $resourceMetadata->operations[1];
        $this->assertSame(InvokableController::class, $postOperation->controller);
    }
}

class TestResource
{
    public string $id;
    public string $name;
}
