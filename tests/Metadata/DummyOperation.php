<?php

namespace Pre<PERSON>ero\ApiBundle\Tests\Metadata;

use Pre<PERSON>ero\ApiBundle\Attribute\Operation\Operation;
use Pre<PERSON>ero\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Enum\Pagination;

class DummyOperation extends Operation
{
    public const string HTTP_METHOD = 'GET';

    public function __construct(
        string|array $controller = ['PreZero\\ApiBundle\\Tests\\Controller\\DummyController', 'getItem'],
        ?string $name = null,
        string $summary = '',
        string $description = '',
        ?string $security = null,
        int $routePriority = 0,
        ?string $uriTemplate = null,
        ?array $pathParameters = null,
        array $queryParameters = [],
        bool $omitDefaultQueryParameters = false,
        ContentType $requestType = ContentType::EMPTY,
        string $requestDescription = '',
        array $denormalizationContext = [],
        array $filters = [],
        ?string $input = null,
        array $additionalRequestHeaders = [],
        Pagination $pagination = Pagination::NONE,
        string $responseDescription = '',
        array $responses = [],
        ContentType $responseType = ContentType::DTO,
        array $normalizationContext = [],
        ?string $output = null,
        int $successHttpCode = 200,
        ?string $requestOpenApiSchemaName = null,
        ?string $responseOpenApiSchemaName = null,
        array $responseCodesLogLevel = [],
    ) {
        parent::__construct(
            controller: $controller,
            name: $name,
            summary: $summary,
            description: $description,
            security: $security,
            uriTemplate: $uriTemplate,
            routePriority: $routePriority,
            pathParameters: $pathParameters,
            queryParameters: $queryParameters,
            omitDefaultQueryParameters: $omitDefaultQueryParameters,
            requestType: $requestType,
            requestDescription: $requestDescription,
            denormalizationContext: $denormalizationContext,
            filters: $filters,
            input: $input,
            additionalRequestHeaders: $additionalRequestHeaders,
            requestOpenApiSchemaName: $requestOpenApiSchemaName,
            pagination: $pagination,
            maxPerPage: null,
            defaultPerPage: null,
            responseDescription: $responseDescription,
            responses: $responses,
            responseType: $responseType,
            normalizationContext: $normalizationContext,
            output: $output,
            successHttpCode: $successHttpCode,
            responseOpenApiSchemaName: $responseOpenApiSchemaName,
            responseCodesLogLevel: $responseCodesLogLevel,
        );
    }
}
