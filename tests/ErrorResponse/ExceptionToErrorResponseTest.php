<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Tests\ErrorResponse;

use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use PreZero\ApiBundle\ApiRequestMetadata;
use PreZero\ApiBundle\ApiRequestMetadataResolver;
use PreZero\ApiBundle\Enum\ContentType;
use PreZero\ApiBundle\Enum\Pagination;
use PreZero\ApiBundle\ErrorResponse\ApiErrorResponse;
use PreZero\ApiBundle\ErrorResponse\ExceptionToErrorResponse;
use PreZero\ApiBundle\ErrorResponse\ValidationError;
use PreZero\ApiBundle\Exception\ApiExceptionInterface;
use PreZero\ApiBundle\Exception\ValidationException;
use PreZero\ApiBundle\JsonResponse;
use PreZero\ApiBundle\Metadata\OperationMetadata;
use PreZero\ApiBundle\Metadata\ResourceMetadata;
use PreZero\ApiBundle\Tests\Controller\DummyController;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;

class ExceptionToErrorResponseTest extends TestCase
{
    private MockObject&ApiRequestMetadataResolver $apiRequestMetadataResolver;
    private MockObject&LoggerInterface $logger;
    private ExceptionToErrorResponse $exceptionToErrorResponse;

    protected function setUp(): void
    {
        $this->apiRequestMetadataResolver = $this->createMock(ApiRequestMetadataResolver::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->exceptionToErrorResponse = new ExceptionToErrorResponse(
            apiResources: $this->apiRequestMetadataResolver,
            logger: $this->logger,
        );
    }

    private function createDummyOperationMetadata(string $name = 'operation'): OperationMetadata
    {
        return new OperationMetadata(
            name: $name,
            controller: [DummyController::class, 'dummyAction'],
            security: null,
            summary: 'Dummy summary',
            description: 'Dummy description',
            type: 'item',
            method: 'GET',
            uriTemplate: '/dummy',
            fullUrlTemplate: '/api/dummy',
            urlRequirements: [],
            routePriority: 0,
            pathParameters: [],
            queryParameters: [],
            denormalizationContext: [],
            filters: [],
            input: null,
            requestType: ContentType::DTO,
            requestDescription: 'Dummy request description',
            headers: [],
            requestOpenApiSchemaName: null,
            pagination: Pagination::NONE,
            maxPerPage: 100,
            defaultPerPage: 10,
            responses: [],
            responseDescription: 'Dummy response description',
            normalizationContext: [],
            successHttpCode: 200,
            output: null,
            responseType: ContentType::DTO,
            responseOpenApiSchemaName: null,
            responseCodesLogLevel: []
        );
    }

    public function testInvokeNoApiMetadataReturns(): void
    {
        $request = new Request();
        $event = new ExceptionEvent(
            $this->createMock(HttpKernelInterface::class),
            $request,
            HttpKernelInterface::MAIN_REQUEST,
            new \Exception()
        );

        $this->apiRequestMetadataResolver
            ->expects(self::once())
            ->method('resolveCurrentOperation')
            ->with($request)
            ->willReturn(null);

        ($this->exceptionToErrorResponse)($event);

        self::assertNull($event->getResponse());
    }

    public function testInvokeValidationExceptionSetsResponse(): void
    {
        $request = new Request();
        $exception = new ValidationException(
            message: 'Validation failed',
            validationErrors: [new ValidationError(field: 'field', message: 'Error')]
        );
        $event = new ExceptionEvent(
            $this->createMock(HttpKernelInterface::class),
            $request,
            HttpKernelInterface::MAIN_REQUEST,
            $exception
        );

        $this->apiRequestMetadataResolver
            ->expects(self::once())
            ->method('resolveCurrentOperation')
            ->willReturn(new ApiRequestMetadata(new ResourceMetadata(
                resourceClassNamespace: 'Test\Namespace',
                resourceClass: 'TestResource',
                resourceClassShortName: 'TestResource',
                name: 'test-resource',
                area: 'test-area',
                identifier: 'id',
                pathPrefix: '/test',
                tag: 'Test',
                security: null,
                operations: [],
                maxPerPage: 100,
                defaultPerPage: 10,
            ), $this->createDummyOperationMetadata()));

        ($this->exceptionToErrorResponse)($event);

        $response = $event->getResponse();
        self::assertInstanceOf(JsonResponse::class, $response);
        self::assertSame(422, $response->getStatusCode());

        $expectedData = new ApiErrorResponse(
            type: 'validation_error',
            title: 'Validation error',
            detail: 'Validation failed',
            violations: [new ValidationError(field: 'field', message: 'Error')]
        );
        self::assertEquals($expectedData, $response->getDataForSerializing());
    }

    public function testInvokeBadRequestHttpExceptionSetsResponse(): void
    {
        $request = new Request();
        $exception = new BadRequestHttpException('Bad request data');
        $event = new ExceptionEvent(
            $this->createMock(HttpKernelInterface::class),
            $request,
            HttpKernelInterface::MAIN_REQUEST,
            $exception
        );

        $this->apiRequestMetadataResolver
            ->expects(self::once())
            ->method('resolveCurrentOperation')
            ->willReturn(new ApiRequestMetadata(new ResourceMetadata(
                resourceClassNamespace: 'Test\Namespace',
                resourceClass: 'TestResource',
                resourceClassShortName: 'TestResource',
                name: 'test-resource',
                area: 'test-area',
                identifier: 'id',
                pathPrefix: '/test',
                tag: 'Test',
                security: null,
                operations: [],
                maxPerPage: 100,
                defaultPerPage: 10,
            ), $this->createDummyOperationMetadata()));

        ($this->exceptionToErrorResponse)($event);

        $response = $event->getResponse();
        self::assertInstanceOf(JsonResponse::class, $response);
        self::assertSame(400, $response->getStatusCode());

        $expectedData = new ApiErrorResponse(
            type: 'bad_request_data',
            title: 'Request cannot be processed.',
            detail: 'Bad request data'
        );
        self::assertEquals($expectedData, $response->getDataForSerializing());
    }

    public function testInvokeAuthenticationExceptionSetsResponse(): void
    {
        $request = new Request();
        $exception = new AuthenticationException('Auth failed');
        $event = new ExceptionEvent(
            $this->createMock(HttpKernelInterface::class),
            $request,
            HttpKernelInterface::MAIN_REQUEST,
            $exception
        );

        $this->apiRequestMetadataResolver
            ->expects(self::once())
            ->method('resolveCurrentOperation')
            ->willReturn(new ApiRequestMetadata(new ResourceMetadata(
                resourceClassNamespace: 'Test\Namespace',
                resourceClass: 'TestResource',
                resourceClassShortName: 'TestResource',
                name: 'test-resource',
                area: 'test-area',
                identifier: 'id',
                pathPrefix: '/test',
                tag: 'Test',
                security: null,
                operations: [],
                maxPerPage: 100,
                defaultPerPage: 10,
            ), $this->createDummyOperationMetadata()));

        ($this->exceptionToErrorResponse)($event);

        $response = $event->getResponse();
        self::assertInstanceOf(JsonResponse::class, $response);
        self::assertSame(401, $response->getStatusCode());

        $expectedData = new ApiErrorResponse(
            type: 'authentication_exception',
            title: 'Authentication failed',
            detail: 'Auth failed'
        );
        self::assertEquals($expectedData, $response->getDataForSerializing());
    }

    public function testInvokeHttpException403SetsResponse(): void
    {
        $request = new Request();
        $exception = new HttpException(statusCode: 403, message: 'Access denied');
        $event = new ExceptionEvent(
            $this->createMock(HttpKernelInterface::class),
            $request,
            HttpKernelInterface::MAIN_REQUEST,
            $exception
        );

        $this->apiRequestMetadataResolver
            ->expects(self::once())
            ->method('resolveCurrentOperation')
            ->willReturn(new ApiRequestMetadata(new ResourceMetadata(
                resourceClassNamespace: 'Test\Namespace',
                resourceClass: 'TestResource',
                resourceClassShortName: 'TestResource',
                name: 'test-resource',
                area: 'test-area',
                identifier: 'id',
                pathPrefix: '/test',
                tag: 'Test',
                security: null,
                operations: [],
                maxPerPage: 100,
                defaultPerPage: 10,
            ), $this->createDummyOperationMetadata()));

        ($this->exceptionToErrorResponse)($event);

        $response = $event->getResponse();
        self::assertInstanceOf(JsonResponse::class, $response);
        self::assertSame(403, $response->getStatusCode());

        $expectedData = new ApiErrorResponse(
            type: 'access_denied',
            title: 'Access denied',
            detail: 'Access denied'
        );
        self::assertEquals($expectedData, $response->getDataForSerializing());
    }

    public function testInvokeHttpExceptionSetsResponse(): void
    {
        $request = new Request();
        $exception = new HttpException(statusCode: 500, message: 'Server error');
        $event = new ExceptionEvent(
            $this->createMock(HttpKernelInterface::class),
            $request,
            HttpKernelInterface::MAIN_REQUEST,
            $exception
        );

        $this->apiRequestMetadataResolver
            ->expects(self::once())
            ->method('resolveCurrentOperation')
            ->willReturn(new ApiRequestMetadata(new ResourceMetadata(
                resourceClassNamespace: 'Test\Namespace',
                resourceClass: 'TestResource',
                resourceClassShortName: 'TestResource',
                name: 'test-resource',
                area: 'test-area',
                identifier: 'id',
                pathPrefix: '/test',
                tag: 'Test',
                security: null,
                operations: [],
                maxPerPage: 100,
                defaultPerPage: 10,
            ), $this->createDummyOperationMetadata()));

        ($this->exceptionToErrorResponse)($event);

        $response = $event->getResponse();
        self::assertInstanceOf(JsonResponse::class, $response);
        self::assertSame(500, $response->getStatusCode());

        $expectedData = new ApiErrorResponse(
            type: 'http_exception',
            title: 'Server error',
            detail: 'Server error'
        );
        self::assertEquals($expectedData, $response->getDataForSerializing());
    }

    public function testInvokeApiExceptionInterfaceSetsResponse(): void
    {
        $request = new Request();
        $exception = new class('api type', 'api title', 'api detail', 'api translation', 503) extends \Exception implements ApiExceptionInterface {
            public function __construct(
                private string $type,
                private string $title,
                private ?string $detail,
                private ?string $translation,
                int $code,
            ) {
                parent::__construct(message: $this->title, code: $code);
            }

            public function getType(): string
            {
                return $this->type;
            }

            public function getTitle(): string
            {
                return $this->title;
            }

            public function getDetail(): ?string
            {
                return $this->detail;
            }

            public function getTranslation(): ?string
            {
                return $this->translation;
            }
        };
        $event = new ExceptionEvent(
            $this->createMock(HttpKernelInterface::class),
            $request,
            HttpKernelInterface::MAIN_REQUEST,
            $exception
        );

        $this->apiRequestMetadataResolver
            ->expects(self::once())
            ->method('resolveCurrentOperation')
            ->willReturn(new ApiRequestMetadata(new ResourceMetadata(
                resourceClassNamespace: 'Test\Namespace',
                resourceClass: 'TestResource',
                resourceClassShortName: 'TestResource',
                name: 'test-resource',
                area: 'test-area',
                identifier: 'id',
                pathPrefix: '/test',
                tag: 'Test',
                security: null,
                operations: [],
                maxPerPage: 100,
                defaultPerPage: 10,
            ), $this->createDummyOperationMetadata()));

        ($this->exceptionToErrorResponse)($event);

        $response = $event->getResponse();
        self::assertInstanceOf(JsonResponse::class, $response);
        self::assertSame(503, $response->getStatusCode());

        $expectedData = new ApiErrorResponse(
            type: 'api type',
            title: 'api title',
            detail: 'api detail',
            translation: 'api translation'
        );
        self::assertEquals($expectedData, $response->getDataForSerializing());
    }

    public function testInvokeGenericExceptionNoResponseSet(): void
    {
        $request = new Request();
        $exception = new \Exception('Generic error');
        $event = new ExceptionEvent(
            $this->createMock(HttpKernelInterface::class),
            $request,
            HttpKernelInterface::MAIN_REQUEST,
            $exception
        );

        $this->apiRequestMetadataResolver
            ->expects(self::once())
            ->method('resolveCurrentOperation')
            ->willReturn(new ApiRequestMetadata(new ResourceMetadata(
                resourceClassNamespace: 'Test\Namespace',
                resourceClass: 'TestResource',
                resourceClassShortName: 'TestResource',
                name: 'test-resource',
                area: 'test-area',
                identifier: 'id',
                pathPrefix: '/test',
                tag: 'Test',
                security: null,
                operations: [],
                maxPerPage: 100,
                defaultPerPage: 10,
            ), $this->createDummyOperationMetadata()));

        ($this->exceptionToErrorResponse)($event);

        self::assertNull($event->getResponse(), 'Response should not be set for generic exceptions not handled explicitly.');
    }
}
