# Upgrade Guide

This document provides step-by-step instructions for upgrading your application to use the latest version of the API Bundle.

## Version 2.0.0

### Breaking Changes

#### URL Requirements Consolidation

**What Changed:**
- The separate `urlRequirements` property has been removed from all Operation classes
- URL constraints are now defined directly on `PathParameter` objects via a new `constraint` property
- This consolidates URL requirements with path parameter definitions for better organization and consistency

**Migration Required:**
All operations that previously used `urlRequirements` must be updated to use `PathParameter` constraints instead.

#### Before (Old Way)

```php
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Value\PathParameter;

new Get(
    controller: MyController::class,
    controllerAction: 'getItem',
    urlRequirements: ['id' => '\d+', 'slug' => '[a-z0-9-]+'],
    pathParameters: [
        new PathParameter('id', 'integer', 'The item ID'),
        new PathParameter('slug', 'string', 'The item slug'),
    ]
)
```

#### After (New Way)

```php
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Value\PathParameter;

new Get(
    controller: MyController::class,
    controllerAction: 'getItem',
    pathParameters: [
        new PathParameter('id', 'integer', 'The item ID', '\d+'),
        new PathParameter('slug', 'string', 'The item slug', '[a-z0-9-]+'),
    ]
)
```

### Migration Steps

1. **Identify Operations with URL Requirements**

   Search your codebase for operations that use the `urlRequirements` parameter:
   ```bash
   grep -r "urlRequirements" src/
   ```

2. **Update Each Operation**

   For each operation found:

   a. **Remove the `urlRequirements` parameter**

   b. **Add or update `pathParameters`** to include constraints:
      - If `pathParameters` already exist, add the `constraint` parameter to the corresponding `PathParameter`
      - If `pathParameters` don't exist, create them with the constraints from `urlRequirements`

   c. **Match parameter names** between `urlRequirements` keys and `PathParameter` names

3. **Example Migration**

   **Before:**
   ```php
   new Put(
       controller: ProductController::class,
       controllerAction: 'updateProduct',
       urlRequirements: [
           'categoryId' => '\d+',
           'productId' => '[a-f0-9-]{36}',
       ],
       pathParameters: [
           new PathParameter('categoryId', 'integer', 'Category ID'),
           new PathParameter('productId', 'string', 'Product UUID'),
       ]
   )
   ```

   **After:**
   ```php
   new Put(
       controller: ProductController::class,
       controllerAction: 'updateProduct',
       pathParameters: [
           new PathParameter('categoryId', 'integer', 'Category ID', '\d+'),
           new PathParameter('productId', 'string', 'Product UUID', '[a-f0-9-]{36}'),
       ]
   )
   ```

4. **Handle Missing PathParameters**

   If an operation had `urlRequirements` but no `pathParameters`, create them:

   **Before:**
   ```php
   new Delete(
       controller: ItemController::class,
       controllerAction: 'deleteItem',
       urlRequirements: ['id' => '\d+']
   )
   ```

   **After:**
   ```php
   new Delete(
       controller: ItemController::class,
       controllerAction: 'deleteItem',
       pathParameters: [
           new PathParameter('id', 'integer', 'Item ID', '\d+'),
       ]
   )
   ```

### Benefits of the New Approach

1. **Better Organization**: URL constraints are now co-located with their parameter definitions
2. **Improved Consistency**: Single source of truth for path parameter information
3. **Enhanced Readability**: Parameter name, type, description, and constraint are all in one place
4. **Reduced Duplication**: No need to define parameter names in multiple places

### Common Constraint Patterns

Here are some common regex patterns you might be migrating:

- **Integer IDs**: `\d+`
- **UUIDs**: `[a-f0-9-]{36}` or `[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}`
- **Slugs**: `[a-z0-9-]+`
- **Alphanumeric codes**: `[A-Z0-9]+`
- **Date formats**: `\d{4}-\d{2}-\d{2}`

### Validation

After migration, ensure your changes work correctly:

1. **Run tests** to verify functionality
2. **Check route generation** to ensure constraints are applied
3. **Test API endpoints** to confirm URL validation works as expected

### Need Help?

If you encounter issues during migration:

1. Check that parameter names in `PathParameter` objects match the keys from your old `urlRequirements`
2. Verify that constraint patterns are valid regex expressions
3. Ensure all operations that had `urlRequirements` have been updated
4. Run your test suite to catch any missed migrations

---

#### Controller Reference Format Update

**What Changed:**
- The separate `controller` and `controllerAction` parameters have been replaced with a single `controller` parameter
- The new parameter accepts either a callable array format `[ClassName::class, 'methodName']` or an invokable class format `ClassName::class`
- This provides better IDE support with proper method resolution and autocompletion

**Migration Required:**
All operations must be updated to use the new controller reference format.

#### Before (Old Way)

```php
use PreZero\ApiBundle\Attribute\Operation\Get;

new Get(
    controller: MyController::class,
    controllerAction: 'getItem'
)
```

#### After (New Way)

```php
use PreZero\ApiBundle\Attribute\Operation\Get;

// For regular controllers with specific methods
new Get(
    controller: [MyController::class, 'getItem']
)

// For invokable controllers (classes with __invoke method)
new Get(
    controller: InvokableController::class
)
```

### Migration Steps

1. **Update Regular Controllers**

   Replace separate `controller` and `controllerAction` parameters with a single `controller` array:

   ```php
   // Before
   new Post(
       controller: UserController::class,
       controllerAction: 'createUser'
   )

   // After
   new Post(
       controller: [UserController::class, 'createUser']
   )
   ```

2. **Update Invokable Controllers**

   For controllers that use the `__invoke` method, use the class name directly:

   ```php
   // Before
   new Get(
       controller: GetUserController::class,
       controllerAction: '__invoke'
   )

   // After
   new Get(
       controller: GetUserController::class
   )
   ```

### Benefits of the New Approach

1. **Better IDE Support**: IDEs can now properly resolve method references and provide autocompletion
2. **Type Safety**: PHP's callable type checking helps catch errors at development time
3. **Cleaner Syntax**: Single parameter instead of two separate ones
4. **Consistency**: Follows PHP's standard callable format

---

#### Pagination Configuration System Overhaul

**What Changed:**
- The enum-based `per_page_options` arrays have been replaced with `max_per_page` integer values
- Pagination now uses a maximum value approach instead of predefined allowed values
- The minimum per-page value is hardcoded to 1 (not configurable)
- Default per-page values are validated to be between 1 and the maximum value
- OpenAPI specifications now show min/max ranges instead of enum values

**Migration Required:**
All pagination configurations at global, area, resource, and operation levels must be updated.

#### Before (Old Way)

**Global Configuration (config/packages/api.yaml):**
```yaml
api:
    per_page_options: [5, 10, 25, 50, 100]
    default_per_page: 25
    areas:
        my_api:
            resource_path: 'src/Entity'
            per_page_options: [10, 20, 50]
            default_per_page: 20
```

**Resource Level:**
```php
#[ApiResource(
    area: 'my_api',
    perPageOptions: [5, 15, 25],
    defaultPerPage: 15,
    operations: [
        new GetCollection(
            controller: [MyController::class, 'getCollection'],
            perPageOptions: [5, 10],
            defaultPerPage: 5
        )
    ]
)]
class MyResource
{
    // ...
}
```

#### After (New Way)

**Global Configuration (config/packages/api.yaml):**
```yaml
api:
    max_per_page: 100
    default_per_page: 25
    areas:
        my_api:
            resource_path: 'src/Entity'
            max_per_page: 50
            default_per_page: 20
```

**Resource Level:**
```php
#[ApiResource(
    area: 'my_api',
    maxPerPage: 25,
    defaultPerPage: 15,
    operations: [
        new GetCollection(
            controller: [MyController::class, 'getCollection'],
            maxPerPage: 10,
            defaultPerPage: 5
        )
    ]
)]
class MyResource
{
    // ...
}
```

### Migration Steps

1. **Update Global Configuration**

   In your `config/packages/api.yaml`:
   ```yaml
   # Before
   api:
       per_page_options: [5, 10, 25, 50, 100]
       default_per_page: 25

   # After
   api:
       max_per_page: 100
       default_per_page: 25
   ```

2. **Update Area Configurations**

   ```yaml
   # Before
   areas:
       my_api:
           per_page_options: [10, 20, 50]
           default_per_page: 20

   # After
   areas:
       my_api:
           max_per_page: 50
           default_per_page: 20
   ```

3. **Update Resource Attributes**

   ```php
   // Before
   #[ApiResource(
       area: 'my_api',
       perPageOptions: [5, 15, 25],
       defaultPerPage: 15
   )]

   // After
   #[ApiResource(
       area: 'my_api',
       maxPerPage: 25,
       defaultPerPage: 15
   )]
   ```

4. **Update Operation Attributes**

   ```php
   // Before
   new GetCollection(
       controller: [MyController::class, 'getCollection'],
       perPageOptions: [5, 10, 20],
       defaultPerPage: 10
   )

   // After
   new GetCollection(
       controller: [MyController::class, 'getCollection'],
       maxPerPage: 20,
       defaultPerPage: 10
   )
   ```

### Validation Rules

The new system enforces these validation rules:

1. **Maximum per-page value** must be >= 1
2. **Default per-page value** must be >= 1 and <= maximum per-page value
3. **Minimum per-page value** is always 1 (hardcoded, not configurable)

### Priority Hierarchy

The configuration priority remains the same:
- **Global** → **Area** → **Resource** → **Operation**
- Lower levels override higher levels
- Each level validates that its default <= its effective maximum

### Benefits of the New Approach

1. **Flexibility**: Clients can choose any value within the 1 to max range
2. **Simplicity**: Single maximum value instead of maintaining arrays
3. **Better Validation**: Clear min/max constraints with helpful error messages
4. **OpenAPI Compliance**: Uses standard min/max schema properties instead of enums

### Common Migration Patterns

**Converting enum arrays to max values:**
- `[5, 10, 25, 50, 100]` → `max_per_page: 100`
- `[10, 20, 50]` → `max_per_page: 50`
- `[5, 15]` → `max_per_page: 15`

**Ensuring valid defaults:**
- If your old default was in the enum, keep it (if <= new max)
- If your old default was the highest enum value, it becomes the new max
- If your old default > new max, reduce it to <= max

### Validation

After migration:

1. **Check configuration syntax** - ensure YAML is valid
2. **Verify default values** - ensure all defaults are <= their respective maximums
3. **Test API endpoints** - confirm pagination works with any value from 1 to max
4. **Review OpenAPI specs** - verify they show min: 1, max: {your_max} instead of enum arrays
